<!DOCTYPE html>
<html>
<head>
    <title>Chart Test</title>
</head>
<body>
    <h1>Chart Test</h1>
    <script>
        // Simple test to check if charts are working
        fetch('http://localhost:5173')
            .then(response => response.text())
            .then(html => {
                console.log('Page loaded successfully');
                
                // Check if candlestick divs exist
                const candlestickMatches = html.match(/id="candlestick-[145]"/g);
                console.log('Candlestick divs found:', candlestickMatches);
                
                // Check if small chart components exist
                const smallChartMatches = html.match(/chart-id="small-chart-[0-9]+-dynamic"/g);
                console.log('Small chart components found:', smallChartMatches);
            })
            .catch(error => {
                console.error('Error:', error);
            });
    </script>
</body>
</html>
